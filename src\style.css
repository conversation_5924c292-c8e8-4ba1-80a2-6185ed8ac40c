/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #111827;
  color: white;
  min-height: 100vh;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* Utility classes */
.bg-gray-900 { background-color: #111827; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-600 { background-color: #4b5563; }
.bg-black { background-color: #000000; }
.bg-cyan-500 { background-color: #06b6d4; }
.bg-cyan-600 { background-color: #0891b2; }
.bg-cyan-700 { background-color: #0e7490; }
.bg-cyan-800 { background-color: #155e75; }
.bg-yellow-500 { background-color: #eab308; }
.bg-yellow-600 { background-color: #ca8a04; }
.bg-red-400 { background-color: #f87171; }
.bg-green-400 { background-color: #4ade80; }

.text-white { color: #ffffff; }
.text-gray-200 { color: #e5e7eb; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-cyan-400 { color: #22d3ee; }
.text-yellow-300 { color: #fde047; }
.text-yellow-400 { color: #facc15; }
.text-red-400 { color: #f87171; }
.text-green-400 { color: #4ade80; }

.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-cyan-500 { border-color: #06b6d4; }
.border-yellow-500 { border-color: #eab308; }
.border-gray-600 { border-color: #4b5563; }
.border-dashed { border-style: dashed; }

.rounded-lg { border-radius: 0.5rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.pt-2 { padding-top: 0.5rem; }
.pr-2 { padding-right: 0.5rem; }
.pb-4 { padding-bottom: 1rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mr-3 { margin-right: 0.75rem; }
.ml-2 { margin-left: 0.5rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.w-6 { width: 1.5rem; }
.w-10 { width: 2.5rem; }
.w-full { width: 100%; }
.h-6 { height: 1.5rem; }
.min-h-screen { min-height: 100vh; }
.max-w-md { max-width: 28rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-7xl { max-width: 80rem; }
.max-h-96 { max-height: 24rem; }

.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.italic { font-style: italic; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.flex-grow { flex-grow: 1; }
.flex-shrink-0 { flex-shrink: 0; }

.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.mx-auto { margin-left: auto; margin-right: auto; }

.z-50 { z-index: 50; }

.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }

.transform { transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1); }

.list-disc { list-style-type: disc; }
.list-inside { list-style-position: inside; }

.overflow-y-auto { overflow-y: auto; }

/* Custom styles */
.bg-opacity-70 { background-color: rgba(0, 0, 0, 0.7); }
.bg-opacity-80 { background-color: rgba(0, 0, 0, 0.8); }

.bg-yellow-500\/20 { background-color: rgba(234, 179, 8, 0.2); }
.bg-yellow-500\/40 { background-color: rgba(234, 179, 8, 0.4); }
.bg-yellow-500\/10 { background-color: rgba(234, 179, 8, 0.1); }
.bg-gray-700\/30 { background-color: rgba(55, 65, 81, 0.3); }

/* Button styles */
button {
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: inherit;
}

button:hover {
  transform: translateY(-1px);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

button:disabled:hover {
  transform: none;
}

/* Hover effects */
.hover\:bg-cyan-600:hover { background-color: #0891b2; }
.hover\:bg-cyan-800:hover { background-color: #155e75; }
.hover\:bg-yellow-600:hover { background-color: #ca8a04; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:text-cyan-400:hover { color: #22d3ee; }
.hover\:text-red-400:hover { color: #f87171; }
.hover\:ring-2:hover { box-shadow: 0 0 0 2px; }
.hover\:ring-cyan-500:hover { box-shadow: 0 0 0 2px #06b6d4; }
.hover\:scale-105:hover { transform: scale(1.05); }

/* Disabled states */
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.disabled\:bg-gray-600:disabled { background-color: #4b5563; }
.disabled\:transform-none:disabled { transform: none; }

/* Grid layouts */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.gap-8 { gap: 2rem; }

/* Responsive design */
@media (min-width: 640px) {
  .sm\:p-6 { padding: 1.5rem; }
  .sm\:text-5xl { font-size: 3rem; line-height: 1; }
}

@media (min-width: 1024px) {
  .lg\:p-8 { padding: 2rem; }
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .lg\:max-h-full { max-height: 100%; }
}

/* Custom component styles */
.game-container {
  background-color: #111827;
  color: white;
  min-height: 100vh;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 1rem;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  z-index: 50;
}

.modal-content {
  background-color: #1f2937;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 2rem;
  max-width: 28rem;
  width: 100%;
  text-align: center;
  border: 1px solid #06b6d4;
}

.item-button {
  width: 100%;
  text-align: left;
  display: flex;
  align-items: center;
  background-color: #374151;
  border-radius: 0.5rem;
  padding: 0.75rem;
  transition: all 0.2s ease;
  border: none;
  color: #e5e7eb;
}

.item-button:hover {
  background-color: #155e75;
  box-shadow: 0 0 0 2px #06b6d4;
}

.item-dot {
  width: 1.5rem;
  height: 1.5rem;
  background-color: #0e7490;
  border-radius: 50%;
  flex-shrink: 0;
  margin-right: 0.75rem;
}

.ranking-slot {
  display: flex;
  align-items: center;
  border-radius: 0.5rem;
  padding: 0.625rem;
  transition: all 0.3s ease;
}

.ranking-slot.filled {
  background-color: #374151;
}

.ranking-slot.empty {
  background-color: rgba(55, 65, 81, 0.3);
  border: 2px dashed #4b5563;
}

.ranking-slot.hinted {
  background-color: rgba(234, 179, 8, 0.1);
}

.rank-number {
  font-size: 1.5rem;
  font-weight: 700;
  width: 2.5rem;
  text-align: center;
  color: #22d3ee;
}

.rank-number.hinted {
  color: #facc15;
}

.control-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
}

.control-button {
  background: none;
  border: none;
  padding: 0.25rem;
  cursor: pointer;
  transition: color 0.2s ease;
}

.arrow-button:disabled {
  color: #4b5563;
  cursor: not-allowed;
}

.arrow-button:not(:disabled) {
  color: #d1d5db;
}

.arrow-button:not(:disabled):hover {
  color: #22d3ee;
}

.remove-button {
  color: #6b7280;
}

.remove-button:hover {
  color: #f87171;
}
